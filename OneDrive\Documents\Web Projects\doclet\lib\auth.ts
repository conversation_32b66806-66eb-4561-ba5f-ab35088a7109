import type { NextAuthOptions } from "next-auth";
import GitHubProvider from "next-auth/providers/github";

export const authOptions: NextAuthOptions = {
  providers: [
    GitHubProvider({
      clientId: process.env.GITHUB_ID!,
      clientSecret: process.env.GITHUB_SECRET!,
    }),
  ],
  session: { strategy: "jwt" },
  callbacks: {
    async session({ session, token }) {
      if (session.user && token?.sub) {
        // expose the user id on the session for convenience
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error - augmented in types/next-auth.d.ts
        session.user.id = token.sub;
      }
      return session;
    },
  },
};

